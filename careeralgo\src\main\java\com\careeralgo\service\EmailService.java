package com.careeralgo.service;

import com.careeralgo.model.Application;
import com.careeralgo.model.User;
import com.careeralgo.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * Service for sending email notifications
 * TODO: Integrate with SendGrid or similar email service
 */
@Service
public class EmailService {

    private static final Logger logger = LoggerFactory.getLogger(EmailService.class);

    @Value("${careeralgo.email.enabled:false}")
    private boolean emailEnabled;

    @Value("${careeralgo.email.from-address:<EMAIL>}")
    private String fromAddress;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private SendGridEmailService sendGridEmailService;

    /**
     * Send offer received notification
     */
    public void sendOfferReceivedNotification(String userId, Application application) {
        if (!emailEnabled) {
            logger.debug("Email notifications disabled, skipping offer notification");
            return;
        }

        try {
            User user = userRepository.findById(userId).orElse(null);
            if (user == null || !user.getPreferences().isEmailNotifications()) {
                logger.debug("User not found or email notifications disabled for user: {}", userId);
                return;
            }

            // Send email using SendGrid
            boolean sent = sendGridEmailService.sendOfferReceivedNotification(user, application);
            if (sent) {
                logger.info("Sent offer notification email to: {} for application: {}",
                        user.getEmail(), application.getId());
            } else {
                logger.warn("Failed to send offer notification email to: {}", user.getEmail());
            }

        } catch (Exception e) {
            logger.error("Failed to send offer notification email", e);
        }
    }

    /**
     * Send interview reminder notification
     */
    public void sendInterviewReminderNotification(String userId, Application application) {
        if (!emailEnabled) {
            return;
        }

        try {
            User user = userRepository.findById(userId).orElse(null);
            if (user == null || !user.getPreferences().isEmailNotifications()) {
                return;
            }

            // Send email using SendGrid
            boolean sent = sendGridEmailService.sendInterviewReminder(user, application);
            if (sent) {
                logger.info("Sent interview reminder email to: {} for application: {}",
                        user.getEmail(), application.getId());
            } else {
                logger.warn("Failed to send interview reminder email to: {}", user.getEmail());
            }

        } catch (Exception e) {
            logger.error("Failed to send interview reminder email", e);
        }
    }

    /**
     * Send weekly application summary
     */
    public void sendWeeklyApplicationSummary(String userId) {
        if (!emailEnabled) {
            return;
        }

        try {
            User user = userRepository.findById(userId).orElse(null);
            if (user == null || !user.getPreferences().isWeeklyReports()) {
                return;
            }

            // Send email using SendGrid
            Map<String, Object> summaryData = new HashMap<>();
            // TODO: Add actual summary data
            boolean sent = sendGridEmailService.sendWeeklySummary(user, summaryData);
            if (sent) {
                logger.info("Sent weekly summary email to: {}", user.getEmail());
            } else {
                logger.warn("Failed to send weekly summary email to: {}", user.getEmail());
            }

        } catch (Exception e) {
            logger.error("Failed to send weekly summary email", e);
        }
    }

    /**
     * Send job alert notification
     */
    public void sendJobAlertNotification(String userId, String jobTitle, int jobCount) {
        if (!emailEnabled) {
            return;
        }

        try {
            User user = userRepository.findById(userId).orElse(null);
            if (user == null || !user.getPreferences().isJobAlerts()) {
                return;
            }

            // Send email using SendGrid
            String searchUrl = "https://careeralgo.com/jobs?search=" + jobTitle;
            boolean sent = sendGridEmailService.sendJobAlert(user, jobTitle, jobCount, searchUrl);
            if (sent) {
                logger.info("Sent job alert email to: {} for {} new {} jobs",
                        user.getEmail(), jobCount, jobTitle);
            } else {
                logger.warn("Failed to send job alert email to: {}", user.getEmail());
            }

        } catch (Exception e) {
            logger.error("Failed to send job alert email", e);
        }
    }

    /**
     * Send welcome email to new user
     */
    public void sendWelcomeEmail(String userId) {
        if (!emailEnabled) {
            return;
        }

        try {
            User user = userRepository.findById(userId).orElse(null);
            if (user == null) {
                return;
            }

            // Send email using SendGrid
            boolean sent = sendGridEmailService.sendWelcomeEmail(user);
            if (sent) {
                logger.info("Sent welcome email to: {}", user.getEmail());
            } else {
                logger.warn("Failed to send welcome email to: {}", user.getEmail());
            }

        } catch (Exception e) {
            logger.error("Failed to send welcome email", e);
        }
    }

    // Email content builders

    private String buildOfferEmailContent(User user, Application application) {
        return String.format("""
            Hi %s,

            Congratulations! You've received a job offer for your application.

            Application Details:
            - Application ID: %s
            - Status: %s

            Please log in to your CareerAlgo dashboard to view the full offer details.

            Best regards,
            The CareerAlgo Team
            """,
            user.getFirstName(),
            application.getId(),
            application.getStatus());
    }

    private String buildInterviewReminderContent(User user, Application application) {
        return String.format("""
            Hi %s,

            This is a reminder about your upcoming interview.

            Application Details:
            - Application ID: %s
            - Status: %s

            Please log in to your CareerAlgo dashboard to view interview details and preparation materials.

            Good luck!
            The CareerAlgo Team
            """,
            user.getFirstName(),
            application.getId(),
            application.getStatus());
    }

    private String buildWeeklySummaryContent(User user) {
        return String.format("""
            Hi %s,

            Here's your weekly job search summary:

            This week you:
            - Applied to X jobs
            - Received Y responses
            - Had Z interviews

            Keep up the great work! Log in to CareerAlgo to see your detailed analytics.

            Best regards,
            The CareerAlgo Team
            """,
            user.getFirstName());
    }

    private String buildJobAlertContent(User user, String jobTitle, int jobCount) {
        return String.format("""
            Hi %s,

            We found %d new %s jobs that match your preferences!

            Log in to CareerAlgo to view these opportunities and apply with one click.

            Happy job hunting!
            The CareerAlgo Team
            """,
            user.getFirstName(),
            jobCount,
            jobTitle);
    }

    private String buildWelcomeEmailContent(User user) {
        return String.format("""
            Hi %s,

            Welcome to CareerAlgo! We're excited to help you accelerate your career journey.

            Here's what you can do next:
            1. Complete your profile
            2. Upload your resume
            3. Start exploring job opportunities
            4. Get AI-powered recommendations

            Get started: https://careeralgo.com/dashboard

            If you have any questions, don't hesitate to reach out to our support team.

            Welcome aboard!
            The CareerAlgo Team
            """,
            user.getFirstName());
    }
}
