package com.careeralgo.service;

import com.careeralgo.constant.ExperienceLevel;
import com.careeralgo.model.*;
import com.careeralgo.repository.JobRepository;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * Service for integrating with TheMuse API to fetch job listings
 */
@Service
public class TheMuseApiService {

    private static final Logger logger = LoggerFactory.getLogger(TheMuseApiService.class);

    @Value("${careeralgo.external-apis.themuse.api-key}")
    private String apiKey;

    @Value("${careeralgo.external-apis.themuse.base-url:https://www.themuse.com/api/public}")
    private String baseUrl;

    @Value("${careeralgo.external-apis.themuse.enabled:true}")
    private boolean enabled;

    @Autowired
    private JobRepository jobRepository;

    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * Fetch jobs from TheMuse API
     */
    public List<Job> fetchJobs(String category, String location, int page, int pageSize) {
        if (!enabled) {
            logger.debug("TheMuse API integration disabled");
            return Collections.emptyList();
        }

        try {
            String url = buildJobSearchUrl(category, location, page, pageSize);
            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                return parseJobsResponse(response.getBody());
            } else {
                logger.warn("TheMuse API returned status: {}", response.getStatusCode());
                return Collections.emptyList();
            }

        } catch (Exception e) {
            logger.error("Error fetching jobs from TheMuse API", e);
            return Collections.emptyList();
        }
    }

    /**
     * Fetch companies from TheMuse API
     */
    public List<Company> fetchCompanies(int page, int pageSize) {
        if (!enabled) {
            logger.debug("TheMuse API integration disabled");
            return Collections.emptyList();
        }

        try {
            String url = buildCompanySearchUrl(page, pageSize);
            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                return parseCompaniesResponse(response.getBody());
            } else {
                logger.warn("TheMuse API returned status: {}", response.getStatusCode());
                return Collections.emptyList();
            }

        } catch (Exception e) {
            logger.error("Error fetching companies from TheMuse API", e);
            return Collections.emptyList();
        }
    }

    /**
     * Sync jobs from TheMuse API (scheduled task)
     */
    @Scheduled(fixedRate = 3600000) // Run every hour
    @Async
    public void syncJobsFromTheMuse() {
        if (!enabled) {
            return;
        }

        logger.info("Starting scheduled job sync from TheMuse API");

        try {
            // Fetch jobs from different categories
            String[] categories = {"Engineering", "Data Science", "Product", "Design", "Marketing", "Sales"};
            int totalJobsSynced = 0;

            for (String category : categories) {
                List<Job> jobs = fetchJobs(category, null, 0, 50);
                int syncedCount = syncJobsToDatabase(jobs);
                totalJobsSynced += syncedCount;

                // Add delay between requests to respect rate limits
                Thread.sleep(1000);
            }

            logger.info("Completed job sync from TheMuse API. Total jobs synced: {}", totalJobsSynced);

        } catch (Exception e) {
            logger.error("Error during scheduled job sync from TheMuse API", e);
        }
    }

    /**
     * Sync specific job by external ID
     */
    public Job syncJobById(String theMuseJobId) {
        if (!enabled) {
            return null;
        }

        try {
            String url = baseUrl + "/jobs/" + theMuseJobId;
            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                JsonNode jobNode = objectMapper.readTree(response.getBody());
                Job job = parseJobFromJson(jobNode);

                if (job != null) {
                    return saveOrUpdateJob(job);
                }
            }

        } catch (Exception e) {
            logger.error("Error syncing job by ID from TheMuse API: {}", theMuseJobId, e);
        }

        return null;
    }

    // Private helper methods

    private String buildJobSearchUrl(String category, String location, int page, int pageSize) {
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(baseUrl + "/jobs")
                .queryParam("page", page)
                .queryParam("page_size", Math.min(pageSize, 100)); // TheMuse max is 100

        if (category != null && !category.isEmpty()) {
            builder.queryParam("category", category);
        }

        if (location != null && !location.isEmpty()) {
            builder.queryParam("location", location);
        }

        return builder.toUriString();
    }

    private String buildCompanySearchUrl(int page, int pageSize) {
        return UriComponentsBuilder.fromHttpUrl(baseUrl + "/companies")
                .queryParam("page", page)
                .queryParam("page_size", Math.min(pageSize, 100))
                .toUriString();
    }

    private List<Job> parseJobsResponse(String responseBody) {
        try {
            JsonNode root = objectMapper.readTree(responseBody);
            JsonNode resultsNode = root.path("results");

            List<Job> jobs = new ArrayList<>();
            for (JsonNode jobNode : resultsNode) {
                Job job = parseJobFromJson(jobNode);
                if (job != null) {
                    jobs.add(job);
                }
            }

            return jobs;

        } catch (Exception e) {
            logger.error("Error parsing jobs response from TheMuse API", e);
            return Collections.emptyList();
        }
    }

    private List<Company> parseCompaniesResponse(String responseBody) {
        try {
            JsonNode root = objectMapper.readTree(responseBody);
            JsonNode resultsNode = root.path("results");

            List<Company> companies = new ArrayList<>();
            for (JsonNode companyNode : resultsNode) {
                Company company = parseCompanyFromJson(companyNode);
                if (company != null) {
                    companies.add(company);
                }
            }

            return companies;

        } catch (Exception e) {
            logger.error("Error parsing companies response from TheMuse API", e);
            return Collections.emptyList();
        }
    }

    private Job parseJobFromJson(JsonNode jobNode) {
        try {
            Job job = new Job();

            // Basic job information
            job.setExternalId(jobNode.path("id").asText());
            job.setSource(Job.JobSource.THEMUSE);
            job.setTitle(jobNode.path("name").asText());
            job.setDescription(jobNode.path("contents").asText());
            job.setApplicationUrl(jobNode.path("refs").path("landing_page").asText());

            // Parse publication date
            String publicationDate = jobNode.path("publication_date").asText();
            if (!publicationDate.isEmpty()) {
                job.setPostedDate(LocalDateTime.parse(publicationDate,
                        DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'")));
            }

            // Parse company information
            JsonNode companyNode = jobNode.path("company");
            if (!companyNode.isMissingNode()) {
                Company company = parseCompanyFromJson(companyNode);
                job.setCompany(company);
            }

            // Parse locations
            JsonNode locationsNode = jobNode.path("locations");
            if (locationsNode.isArray() && locationsNode.size() > 0) {
                JsonNode locationNode = locationsNode.get(0);
                JobLocation jobLocation = parseLocationFromJson(locationNode);
                job.setLocation(jobLocation);
            }

            // Parse categories (map to skills)
            JsonNode categoriesNode = jobNode.path("categories");
            if (categoriesNode.isArray()) {
                List<String> skills = new ArrayList<>();
                for (JsonNode categoryNode : categoriesNode) {
                    skills.add(categoryNode.path("name").asText());
                }
                job.setSkills(skills);
            }

            // Parse levels (map to experience level)
            JsonNode levelsNode = jobNode.path("levels");
            if (levelsNode.isArray() && levelsNode.size() > 0) {
                String level = levelsNode.get(0).path("name").asText().toLowerCase();
                job.setExperienceLevel(mapToExperienceLevel(level));
            }

            // Set default values
            job.setActive(true);
            job.setFeatured(false);
            job.setViewCount(0);
            job.setApplicationCount(0);
            job.setEmploymentType(Job.EmploymentType.FULL_TIME);

            // Generate slug
            job.setSlug(generateSlug(job.getTitle(), job.getExternalId()));

            return job;

        } catch (Exception e) {
            logger.error("Error parsing job from JSON", e);
            return null;
        }
    }

    private Company parseCompanyFromJson(JsonNode companyNode) {
        Company company = new Company();

        company.setName(companyNode.path("name").asText());
        company.setDescription(companyNode.path("description").asText());
        company.setWebsite(companyNode.path("website").asText());
        company.setLogoUrl(companyNode.path("refs").path("logo_image").asText());

        // Parse size
        JsonNode sizeNode = companyNode.path("size");
        if (!sizeNode.isMissingNode()) {
            company.setSize(sizeNode.path("name").asText());
        }

        // Parse industry
        JsonNode industriesNode = companyNode.path("industries");
        if (industriesNode.isArray() && industriesNode.size() > 0) {
            company.setIndustry(industriesNode.get(0).path("name").asText());
        }

        return company;
    }

    private JobLocation parseLocationFromJson(JsonNode locationNode) {
        JobLocation location = new JobLocation();

        location.setCity(locationNode.path("name").asText());

        // TheMuse doesn't provide detailed location breakdown
        // We'll need to parse the name field
        String locationName = locationNode.path("name").asText();
        String[] parts = locationName.split(",");

        if (parts.length >= 2) {
            location.setCity(parts[0].trim());
            location.setState(parts[1].trim());
        }

        if (parts.length >= 3) {
            location.setCountry(parts[2].trim());
        } else {
            location.setCountry("United States"); // Default for TheMuse
        }

        // Check for remote indicators
        location.setRemote(locationName.toLowerCase().contains("remote"));
        location.setHybrid(locationName.toLowerCase().contains("hybrid"));

        return location;
    }

    private ExperienceLevel mapToExperienceLevel(String level) {
        if (level.contains("entry") || level.contains("junior")) {
            return ExperienceLevel.ENTRY_LEVEL;
        } else if (level.contains("mid") || level.contains("intermediate")) {
            return ExperienceLevel.MID_LEVEL;
        } else if (level.contains("senior")) {
            return ExperienceLevel.SENIOR_LEVEL;
        } else if (level.contains("lead") || level.contains("principal")) {
            return ExperienceLevel.LEAD_LEVEL;
        } else if (level.contains("executive") || level.contains("director")) {
            return ExperienceLevel.EXECUTIVE_LEVEL;
        }

        return ExperienceLevel.MID_LEVEL; // Default
    }

    private String generateSlug(String title, String externalId) {
        String slug = title.toLowerCase()
                .replaceAll("[^a-z0-9\\s-]", "")
                .replaceAll("\\s+", "-")
                .replaceAll("-+", "-")
                .replaceAll("^-|-$", "");

        return slug + "-" + externalId;
    }

    private int syncJobsToDatabase(List<Job> jobs) {
        int syncedCount = 0;

        for (Job job : jobs) {
            try {
                Job savedJob = saveOrUpdateJob(job);
                if (savedJob != null) {
                    syncedCount++;
                }
            } catch (Exception e) {
                logger.error("Error saving job to database: {}", job.getExternalId(), e);
            }
        }

        return syncedCount;
    }

    private Job saveOrUpdateJob(Job job) {
        // Check if job already exists
        Optional<Job> existingJob = jobRepository.findByExternalIdAndSource(
                job.getExternalId(), job.getSource());

        if (existingJob.isPresent()) {
            // Update existing job
            Job existing = existingJob.get();
            existing.setTitle(job.getTitle());
            existing.setDescription(job.getDescription());
            existing.setCompany(job.getCompany());
            existing.setLocation(job.getLocation());
            existing.setSkills(job.getSkills());
            existing.setExperienceLevel(job.getExperienceLevel());
            existing.setApplicationUrl(job.getApplicationUrl());
            existing.setLastSyncedAt(LocalDateTime.now());

            return jobRepository.save(existing);
        } else {
            // Save new job
            job.setLastSyncedAt(LocalDateTime.now());
            return jobRepository.save(job);
        }
    }
}
