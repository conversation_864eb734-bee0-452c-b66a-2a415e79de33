package com.careeralgo.security;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.jwt.JwtException;

import javax.crypto.SecretKey;
import java.time.Instant;
import java.util.Date;
import java.util.Map;

/**
 * Custom JWT decoder for Clerk tokens
 */
public class ClerkJwtDecoder implements JwtDecoder {

    private final SecretKey secretKey;

    public ClerkJwtDecoder(String publicKey) {
        // In a real implementation, you would parse the Clerk public key
        // For now, we'll use a placeholder approach
        this.secretKey = Keys.hmacShaKeyFor(publicKey.getBytes());
    }

    @Override
    public Jwt decode(String token) throws JwtException {
        try {
            Claims claims = Jwts.parserBuilder()
                    .setSigningKey(secretKey)
                    .build()
                    .parseClaimsJws(token)
                    .getBody();

            return createJwt(token, claims);
        } catch (Exception e) {
            throw new JwtException("Failed to decode JWT token", e);
        }
    }

    private Jwt createJwt(String token, Claims claims) {
        Instant issuedAt = claims.getIssuedAt() != null ?
                claims.getIssuedAt().toInstant() : Instant.now();
        Instant expiresAt = claims.getExpiration() != null ?
                claims.getExpiration().toInstant() : Instant.now().plus(1, java.time.temporal.ChronoUnit.HOURS);

        Map<String, Object> headers = Map.of(
                "alg", "HS256",
                "typ", "JWT"
        );

        return new Jwt(
                token,
                issuedAt,
                expiresAt,
                headers,
                claims
        );
    }
}
