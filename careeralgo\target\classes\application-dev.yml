# Development Environment Configuration
spring:
    # MongoDB Configuration for Development
    data:
        mongodb:
            uri: mongodb://localhost:27017/careeralgo_dev

    # Redis Configuration for Development
    redis:
        host: localhost
        port: 6379
        password: ""

    # Development Security (Less Strict)
    security:
        oauth2:
            resourceserver:
                jwt:
                    issuer-uri: https://dev-clerk-domain.clerk.accounts.dev

# Development Server Configuration
server:
    port: 8080

# Enhanced Logging for Development
logging:
    level:
        com.careeralgo: DEBUG
        org.springframework.security: DEBUG
        org.springframework.web: DEBUG
        org.springframework.data.mongodb: DEBUG
        org.apache.http: DEBUG
    pattern:
        console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# Development Specific Configuration
careeralgo:
    # Development CORS (More Permissive)
    cors:
        allowed-origins: "http://localhost:3000,http://localhost:3001,http://127.0.0.1:3000"

    # Development Rate Limiting (More Lenient)
    rate-limit:
        enabled: false
        requests-per-minute: 1000

    # Development AI Configuration (Lower Costs)
    ai:
        openai:
            model: gpt-3.5-turbo
            max-tokens: 1000
            temperature: 0.5

# Development Management Endpoints (More Exposed)
management:
    endpoints:
        web:
            exposure:
                include: "*"
    endpoint:
        health:
            show-details: always

# Swagger UI Configuration for Development
springdoc:
    swagger-ui:
        enabled: true
        path: /swagger-ui.html
        try-it-out-enabled: true
    api-docs:
        enabled: true
