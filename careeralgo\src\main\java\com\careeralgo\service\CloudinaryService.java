package com.careeralgo.service;

import com.cloudinary.Cloudinary;
import com.cloudinary.Transformation;
import com.cloudinary.utils.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Map;

/**
 * Service for Cloudinary file storage operations
 */
@Service
public class CloudinaryService {

    private static final Logger logger = LoggerFactory.getLogger(CloudinaryService.class);

    private final Cloudinary cloudinary;

    public CloudinaryService(
            @Value("${careeralgo.storage.cloudinary.cloud-name}") String cloudName,
            @Value("${careeralgo.storage.cloudinary.api-key}") String apiKey,
            @Value("${careeralgo.storage.cloudinary.api-secret}") String apiSecret,
            @Value("${careeralgo.storage.cloudinary.secure}") boolean secure) {

        this.cloudinary = new Cloudinary(ObjectUtils.asMap(
                "cloud_name", cloudName,
                "api_key", apiKey,
                "api_secret", apiSecret,
                "secure", secure
        ));
    }

    /**
     * Upload resume file to Cloudinary
     */
    public UploadResult uploadResume(MultipartFile file, String userId) throws IOException {
        try {
            String publicId = generateResumePublicId(userId, file.getOriginalFilename());

            @SuppressWarnings("unchecked")
            Map<String, Object> uploadParams = ObjectUtils.asMap(
                    "public_id", publicId,
                    "folder", "resumes",
                    "resource_type", "raw", // For non-image files
                    "use_filename", true,
                    "unique_filename", false,
                    "overwrite", false
            );

            @SuppressWarnings("unchecked")
            Map<String, Object> result = cloudinary.uploader().upload(file.getBytes(), uploadParams);

            String url = (String) result.get("secure_url");
            String resultPublicId = (String) result.get("public_id");

            logger.info("Successfully uploaded resume to Cloudinary: {}", url);
            return new UploadResult(url, resultPublicId);

        } catch (IOException e) {
            logger.error("Error uploading resume to Cloudinary", e);
            throw new IOException("Failed to upload file to Cloudinary: " + e.getMessage(), e);
        }
    }

    /**
     * Upload profile picture to Cloudinary
     */
    public UploadResult uploadProfilePicture(MultipartFile file, String userId) throws IOException {
        try {
            String publicId = generateProfilePicturePublicId(userId);

            @SuppressWarnings("unchecked")
            Map<String, Object> uploadParams = ObjectUtils.asMap(
                    "public_id", publicId,
                    "folder", "profile_pictures",
                    "transformation", ObjectUtils.asMap(
                            "width", 300,
                            "height", 300,
                            "crop", "fill",
                            "gravity", "face"
                    ),
                    "overwrite", true
            );

            @SuppressWarnings("unchecked")
            Map<String, Object> result = cloudinary.uploader().upload(file.getBytes(), uploadParams);

            String url = (String) result.get("secure_url");
            String resultPublicId = (String) result.get("public_id");

            logger.info("Successfully uploaded profile picture to Cloudinary: {}", url);
            return new UploadResult(url, resultPublicId);

        } catch (IOException e) {
            logger.error("Error uploading profile picture to Cloudinary", e);
            throw new IOException("Failed to upload profile picture to Cloudinary: " + e.getMessage(), e);
        }
    }

    /**
     * Delete file from Cloudinary
     */
    public void deleteFile(String publicId) throws IOException {
        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> result = cloudinary.uploader().destroy(publicId, ObjectUtils.emptyMap());

            String resultStatus = (String) result.get("result");
            if (!"ok".equals(resultStatus)) {
                logger.warn("File deletion may have failed. Status: {}, PublicId: {}", resultStatus, publicId);
            } else {
                logger.info("Successfully deleted file from Cloudinary: {}", publicId);
            }

        } catch (IOException e) {
            logger.error("Error deleting file from Cloudinary: {}", publicId, e);
            throw new IOException("Failed to delete file from Cloudinary: " + e.getMessage(), e);
        }
    }

    /**
     * Generate optimized URL for file
     */
    public String generateOptimizedUrl(String publicId, Map<String, Object> transformations) {
        @SuppressWarnings("rawtypes")
        Transformation transformation = new Transformation();

        // Apply transformations from map
        for (Map.Entry<String, Object> entry : transformations.entrySet()) {
            transformation.param(entry.getKey(), entry.getValue());
        }

        return cloudinary.url()
                .transformation(transformation)
                .generate(publicId);
    }

    /**
     * Generate thumbnail URL for document
     */
    public String generateThumbnailUrl(String publicId) {
        @SuppressWarnings("unchecked")
        Map<String, Object> transformations = ObjectUtils.asMap(
                "width", 200,
                "height", 260,
                "crop", "fill",
                "format", "jpg",
                "page", 1 // First page for PDFs
        );

        return generateOptimizedUrl(publicId, transformations);
    }

    /**
     * Get file metadata
     */
    public FileMetadata getFileMetadata(String publicId) throws IOException {
        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> result = cloudinary.api().resource(publicId, ObjectUtils.emptyMap());

            String url = (String) result.get("secure_url");
            Integer bytes = (Integer) result.get("bytes");
            String format = (String) result.get("format");
            String createdAt = (String) result.get("created_at");

            return new FileMetadata(url, bytes != null ? bytes.longValue() : 0L, format, createdAt);

        } catch (Exception e) {
            logger.error("Error getting file metadata from Cloudinary: {}", publicId, e);
            throw new IOException("Failed to get file metadata: " + e.getMessage(), e);
        }
    }

    // Helper methods

    private String generateResumePublicId(String userId, String originalFilename) {
        String timestamp = String.valueOf(System.currentTimeMillis());
        String cleanFilename = originalFilename != null ?
                originalFilename.replaceAll("[^a-zA-Z0-9._-]", "_") : "resume";
        return String.format("user_%s_%s_%s", userId, timestamp, cleanFilename);
    }

    private String generateProfilePicturePublicId(String userId) {
        return String.format("user_%s_profile", userId);
    }

    /**
     * Upload result data class
     */
    public static class UploadResult {
        private final String url;
        private final String publicId;

        public UploadResult(String url, String publicId) {
            this.url = url;
            this.publicId = publicId;
        }

        public String getUrl() {
            return url;
        }

        public String getPublicId() {
            return publicId;
        }
    }

    /**
     * File metadata data class
     */
    public static class FileMetadata {
        private final String url;
        private final Long size;
        private final String format;
        private final String createdAt;

        public FileMetadata(String url, Long size, String format, String createdAt) {
            this.url = url;
            this.size = size;
            this.format = format;
            this.createdAt = createdAt;
        }

        public String getUrl() {
            return url;
        }

        public Long getSize() {
            return size;
        }

        public String getFormat() {
            return format;
        }

        public String getCreatedAt() {
            return createdAt;
        }
    }
}
